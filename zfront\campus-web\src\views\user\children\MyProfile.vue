<template>
    <div>
        <el-card class="box-card">
            <div slot="header" class="clearfix">
                <span>基本信息</span>
                <el-button style="float: right; padding: 3px 0" type="text" @click="$emit('personalInformation')">修改信息</el-button>
            </div>
            <div class="content_txt">
                <el-collapse v-model="activeNames">
                    <el-collapse-item title="账号" name="1">
                        <div>{{user.studentId}}</div>
                    </el-collapse-item>
                    <el-collapse-item title="姓名" name="2">
                        <div>{{user.username}}</div>
                    </el-collapse-item>
                    <el-collapse-item title="手机号" name="3">
                        <div>{{user.phone}}</div>
                    </el-collapse-item>
                    <el-collapse-item title="角色" name="4">
                        <div>{{user.role && user.role.name}}</div>
                    </el-collapse-item>
                    <!-- 用户角色的特定信息 -->
                    <template v-if="user.role && user.role.id === 14">
                        <!-- 用户角色不需要显示类别信息 -->
                    </template>
                    <!-- 维修员角色的特定信息 -->
                    <template v-else-if="user.role && user.role.id === 13">
                        <el-collapse-item v-if="user.dept" title="类别" name="5">
                            <div>{{user.dept.name}}</div>
                        </el-collapse-item>
                        <el-collapse-item v-if="user.type" title="子类别" name="6">
                            <div>{{user.type.name}}</div>
                        </el-collapse-item>
                    </template>
                    <!-- 其他角色的特定信息 -->
                    <!-- <template v-else>
                        <el-collapse-item v-if="user.dept" title="部门" name="5">
                            <div>{{user.dept.name}}</div>
                        </el-collapse-item>
                    </template> -->
                    <!-- 维修员地址信息 -->
                    <template v-if="user.role && user.role.id === 13">
                        <el-collapse-item title="地址信息" name="7">
                            <div v-if="user.province && user.city && user.district">
                                <p>{{user.province}} {{user.city}} {{user.district}}</p>
                                <!-- <p v-if="user.address">详细地址: {{user.address}}</p> -->
                            </div>
                            <div v-else>

                                <el-button type="text" @click="showAddressSelector">设置地址</el-button>
                            </div>
                        </el-collapse-item>
                    </template>

                    <el-collapse-item title="余额" name="8">
                        <div><i class="el-icon-money" style="color: red;"> {{user.balance}}元</i></div>
                    </el-collapse-item>
                </el-collapse>
            </div>
        </el-card>

        <!-- 子类别选择对话框 -->
        <el-dialog title="选择子类别" :visible.sync="typeDialogVisible" width="30%">
            <el-form :model="typeForm" label-width="80px">
                <el-form-item label="子类别">
                    <el-select v-model="typeForm.typeId" placeholder="请选择子类别">
                        <el-option
                            v-for="item in typeOptions"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="typeDialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="updateUserType">确 定</el-button>
            </span>
        </el-dialog>

        <!-- 地址选择对话框 -->
        <el-dialog title="设置地址信息" :visible.sync="addressDialogVisible" width="50%">
            <el-form :model="addressForm" label-width="100px">
                <el-form-item label="省市区">
                    <el-cascader
                        v-model="addressForm.region"
                        :options="regionOptions"
                        placeholder="请选择省/市/区"
                        style="width: 100%"
                    ></el-cascader>
                </el-form-item>
                <el-form-item label="详细地址">
                    <el-input
                        v-model="addressForm.address"
                        type="textarea"
                        placeholder="请输入详细地址信息，如街道、门牌号等"
                        :rows="3"
                    ></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="addressDialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="updateUserAddress">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
    import {mapState} from 'vuex'
    import regionData from '@/assets/data/region.js'

    export default {
        name: "MyProfile",
        data() {
            return {
                activeNames: ['1', '2', '3', '4', '5', '6', '7', '8'],
                // 子类别相关
                typeDialogVisible: false,
                typeForm: {
                    typeId: null
                },
                typeOptions: [],

                // 地址相关
                addressDialogVisible: false,
                addressForm: {
                    region: [],
                    address: ''
                },
                regionOptions: regionData
            }
        },
        computed:{
          ...mapState('user',['user'])
        },
        methods: {
            // 显示子类别选择器
            showTypeSelector() {
                // 获取可用的子类别列表
                this.$get("/class/list", { roleId: this.user.role.id, deptId: this.user.dept.id })
                .then(res => {
                    if (res.data.status && res.data.class) {
                        this.typeOptions = res.data.class;
                        console.log('可用的子类别列表:', this.typeOptions);
                        this.typeDialogVisible = true;
                    } else {
                        this.$msg("获取子类别列表失败", "error");
                    }
                })
                .catch(err => {
                    console.error('获取子类别列表失败:', err);
                    this.$msg("获取子类别列表失败，请稍后重试", "error");
                });
            },

            // 更新用户子类别
            updateUserType() {
                if (!this.typeForm.typeId) {
                    this.$msg("请选择子类别", "warning");
                    return;
                }

                // 更新用户信息
                this.$put("/user", {
                    id: this.user.id,
                    classId: this.typeForm.typeId
                })
                .then(res => {
                    if (res.data.status) {
                        this.$msg("子类别设置成功", "success");
                        this.typeDialogVisible = false;

                        // 更新本地用户信息
                        if (res.data.user) {
                            console.log('更新后的用户数据:', res.data.user);
                            this.$store.commit('user/setUser', res.data.user);
                        } else {
                            // 如果返回的数据中没有完整的用户信息，重新获取
                            this.$get("/user/" + this.user.id)
                            .then(rs => {
                                if (rs.data.user) {
                                    console.log('重新获取的用户数据:', rs.data.user);
                                    this.$store.commit('user/setUser', rs.data.user);
                                }
                            });
                        }
                    } else {
                        this.$msg(res.data.msg || "设置失败", "error");
                    }
                })
                .catch(err => {
                    console.error('设置子类别失败:', err);
                    this.$msg("设置失败，请稍后重试", "error");
                });
            },

            // 显示地址选择器
            showAddressSelector() {
                // 如果用户已有地址信息，则预填充表单
                if (this.user.province && this.user.city && this.user.district) {
                    this.addressForm.region = [this.user.province, this.user.city, this.user.district];
                }
                if (this.user.address) {
                    this.addressForm.address = this.user.address;
                }

                this.addressDialogVisible = true;
            },

            // 更新用户地址信息
            updateUserAddress() {
                if (!this.addressForm.region || this.addressForm.region.length < 3) {
                    this.$msg("请选择完整的省市区信息", "warning");
                    return;
                }

                const [province, city, district] = this.addressForm.region;

                // 更新用户信息
                this.$put("/user", {
                    id: this.user.id,
                    province,
                    city,
                    district,
                    address: this.addressForm.address
                })
                .then(res => {
                    if (res.data.status) {
                        this.$msg("地址信息设置成功", "success");
                        this.addressDialogVisible = false;

                        // 更新本地用户信息
                        if (res.data.user) {
                            console.log('更新后的用户数据:', res.data.user);
                            this.$store.commit('user/setUser', res.data.user);
                        } else {
                            // 如果返回的数据中没有完整的用户信息，重新获取
                            this.$get("/user/" + this.user.id)
                            .then(rs => {
                                if (rs.data.user) {
                                    console.log('重新获取的用户数据:', rs.data.user);
                                    this.$store.commit('user/setUser', rs.data.user);
                                }
                            });
                        }
                    } else {
                        this.$msg(res.data.msg || "设置失败", "error");
                    }
                })
                .catch(err => {
                    console.error('设置地址信息失败:', err);
                    this.$msg("设置失败，请稍后重试", "error");
                });
            }
        },
        created() {
            console.log('MyProfile created, user data:', this.user)
            // 确保用户数据已加载
            if (!this.user || !this.user.role) {
                console.warn('User data or role is missing')
            }

            // 打印用户类型信息，用于调试
            if (this.user && this.user.role) {
                console.log('User role ID:', this.user.role.id)
                console.log('User dept:', this.user.dept)
                console.log('User type:', this.user.type)

                // 如果是维修员但没有type信息，尝试重新获取用户数据
                if (this.user.role.id === 13 && !this.user.type) {
                    this.refreshUserData();
                }
            }

            // 添加页面可见性监听器，当页面重新获得焦点时刷新用户数据
            this.addVisibilityListener();
        },
        beforeDestroy() {
            // 移除页面可见性监听器
            this.removeVisibilityListener();
        },
        methods: {
            // 刷新用户数据
            refreshUserData() {
                this.$get("/user/" + this.user.id)
                .then((rs) => {
                    if (rs.data.user) {
                        console.log('Refreshed user data:', JSON.stringify(rs.data.user, null, 2))
                        console.log('User type details:', rs.data.user.type ? JSON.stringify(rs.data.user.type, null, 2) : 'No type data')
                        // 更新用户信息
                        this.$store.commit('user/setUser', rs.data.user)
                    }
                })
                .catch(err => {
                    console.error('刷新用户数据失败:', err);
                });
            },

            // 添加页面可见性监听器
            addVisibilityListener() {
                this.handleVisibilityChange = () => {
                    if (!document.hidden) {
                        // 页面重新获得焦点时，刷新用户数据
                        console.log('页面重新获得焦点，刷新用户数据');
                        this.refreshUserData();
                    }
                };
                document.addEventListener('visibilitychange', this.handleVisibilityChange);
            },

            // 移除页面可见性监听器
            removeVisibilityListener() {
                if (this.handleVisibilityChange) {
                    document.removeEventListener('visibilitychange', this.handleVisibilityChange);
                }
            }
        }
    }
</script>

<style scoped lang="less">
    /deep/ .box-card {
        width: 60%;
        margin: 0 auto;
    }

    /deep/ .el-card__body {
        padding: 0 20px !important;
    }

    /deep/ .el-collapse {
        border-top: none !important;
    }

    /deep/ .el-collapse-item__content {
        padding-bottom: 15px !important;
    }
</style>
